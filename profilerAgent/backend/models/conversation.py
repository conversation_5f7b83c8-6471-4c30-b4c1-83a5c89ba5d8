"""
Conversation related Pydantic models
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

class ConversationStatus(str, Enum):
    """Conversation status enumeration"""
    ACTIVE = "active"
    COMPLETED = "completed"
    PAUSED = "paused"
    ERROR = "error"

class MessageRole(str, Enum):
    """Message role enumeration"""
    USER = "user"
    AGENT = "agent"
    SYSTEM = "system"

class ConversationStart(BaseModel):
    """Start conversation request model"""
    conversation_type: str = Field(default="chat", description="Conversation type (chat/voice)")
    phone_number: Optional[str] = Field(None, description="Phone number for voice conversations")

class ConversationMessage(BaseModel):
    """Send message request model"""
    content: str = Field(..., min_length=1, max_length=1000, description="User message content")
    message_type: str = Field(default="text", description="Message type")
    conversation_id: Optional[str] = Field(None, description="Conversation ID (optional)")

class MessageResponse(BaseModel):
    """Individual message response model"""
    id: str = Field(..., description="Message ID")
    role: MessageRole = Field(..., description="Message role")
    content: str = Field(..., description="Message content")
    timestamp: datetime = Field(..., description="Message timestamp")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional message metadata")

class ConversationResponse(BaseModel):
    """Unified conversation response model - matches UnifiedAgentResponse"""
    content: str = Field(..., description="AI response content")
    agent_type: str = Field(..., description="Agent type (registration/conversation)")
    should_continue: bool = Field(default=True, description="Whether conversation should continue")
    registration_complete: Optional[bool] = Field(None, description="Registration completion status")
    conversation_id: Optional[str] = Field(None, description="Conversation ID")
    progress: Optional[float] = Field(None, description="Registration progress (0.0-1.0)")
    updated_info: Dict[str, Any] = Field(default_factory=dict, description="Updated user information")
    timestamp: datetime = Field(default_factory=datetime.now, description="Response timestamp")

class ConversationHistory(BaseModel):
    """Conversation history model"""
    user_id: str = Field(..., description="User ID")
    messages: List[MessageResponse] = Field(..., description="List of messages")
    total_messages: int = Field(..., description="Total number of messages")
    conversation_started: datetime = Field(..., description="Conversation start time")
    last_activity: datetime = Field(..., description="Last activity timestamp")

class ConversationStatusResponse(BaseModel):
    """Conversation status response model"""
    user_id: str = Field(..., description="User ID")
    status: ConversationStatus = Field(..., description="Current conversation status")
    registration_state: Dict[str, Any] = Field(..., description="Registration state information")
    current_step: str = Field(..., description="Current step in registration/conversation")
    completion_percentage: float = Field(..., ge=0.0, le=1.0, description="Completion percentage")

class VoiceConversationStart(BaseModel):
    """Start voice conversation request model"""
    phone_number: str = Field(..., description="Phone number to call")
    conversation_type: str = Field(default="outbound", description="Voice conversation type")

class VoiceConversationResponse(BaseModel):
    """Voice conversation response model"""
    call_sid: str = Field(..., description="Twilio call SID")
    status: str = Field(..., description="Call status")
    phone_number: str = Field(..., description="Phone number")
    started_at: datetime = Field(..., description="Call start time")
