"""
Redis Client Manager for FastAPI Backend
Async Redis connection management
"""

import logging
from typing import Optional, Any
import redis.asyncio as redis
from redis.asyncio import Redis

# Import AI Agent configuration
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from agent.config.core_config import CoreConfig

logger = logging.getLogger(__name__)

class RedisManager:
    """Async Redis connection manager"""
    
    def __init__(self):
        """Initialize Redis manager"""
        self.client: Optional[Redis] = None
        self.is_connected = False
        
        # Get Redis configuration from CoreConfig
        self.redis_config = CoreConfig.get_redis_config()
        
        logger.info("RedisManager initialized with CoreConfig")
    
    async def connect(self) -> bool:
        """Connect to Redis with retry mechanism"""
        try:
            # Create Redis client
            self.client = redis.from_url(
                self.redis_config["url"],
                max_connections=self.redis_config["max_connections"],
                socket_timeout=self.redis_config["socket_timeout"],
                decode_responses=True  # Automatically decode responses to strings
            )
            
            # Test connection
            await self.client.ping()
            
            self.is_connected = True
            logger.info("Redis connection established")
            return True
            
        except Exception as e:
            logger.error(f"Redis connection error: {e}")
            self.is_connected = False
            return False
    
    async def disconnect(self):
        """Disconnect from Redis"""
        try:
            if self.client:
                await self.client.aclose()
                self.client = None
                self.is_connected = False
                logger.info("Redis connection closed")
        except Exception as e:
            logger.error(f"Error closing Redis connection: {e}")
    
    async def ping(self) -> bool:
        """Test Redis connection"""
        try:
            if not self.client:
                return False
            
            result = await self.client.ping()
            return result is True
            
        except Exception as e:
            logger.error(f"Redis ping failed: {e}")
            return False
    
    async def get(self, key: str) -> Optional[str]:
        """Get value from Redis"""
        try:
            if not self.client:
                return None
            return await self.client.get(key)
        except Exception as e:
            logger.error(f"Redis GET error for key {key}: {e}")
            return None
    
    async def set(self, key: str, value: str, ex: Optional[int] = None) -> bool:
        """Set value in Redis"""
        try:
            if not self.client:
                return False
            
            result = await self.client.set(key, value, ex=ex)
            return result is True
            
        except Exception as e:
            logger.error(f"Redis SET error for key {key}: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete key from Redis"""
        try:
            if not self.client:
                return False
            
            result = await self.client.delete(key)
            return result > 0
            
        except Exception as e:
            logger.error(f"Redis DELETE error for key {key}: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in Redis"""
        try:
            if not self.client:
                return False
            
            result = await self.client.exists(key)
            return result > 0
            
        except Exception as e:
            logger.error(f"Redis EXISTS error for key {key}: {e}")
            return False
    
    async def setex(self, key: str, time: int, value: str) -> bool:
        """Set key with expiration time"""
        try:
            if not self.client:
                return False
            
            result = await self.client.setex(key, time, value)
            return result is True
            
        except Exception as e:
            logger.error(f"Redis SETEX error for key {key}: {e}")
            return False
    
    async def get_info(self) -> dict:
        """Get Redis server information"""
        try:
            if not self.client:
                return {}
            
            info = await self.client.info()
            return {
                "redis_version": info.get("redis_version", "unknown"),
                "connected_clients": info.get("connected_clients", 0),
                "used_memory_human": info.get("used_memory_human", "unknown"),
                "total_commands_processed": info.get("total_commands_processed", 0)
            }
            
        except Exception as e:
            logger.error(f"Redis INFO error: {e}")
            return {}
