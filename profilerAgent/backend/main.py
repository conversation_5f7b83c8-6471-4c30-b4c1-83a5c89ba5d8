"""
FastAPI Main Application Entry Point
AI Dating Agent Backend
"""

import asyncio
import logging
from contextlib import asynccontextmanager
from datetime import datetime
from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

# Import existing AI Agent components
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Direct imports to avoid utils dependency
from agent.core.database_manager import DatabaseManager
from agent.config.core_config import CoreConfig

# Import Redis manager
from .utils.redis_client import RedisManager

# Import API routers
from .api.v1 import auth, users, conversations, profiles, matching
# from .middleware import auth_middleware, rate_limit_middleware

# Setup logging
logging.basicConfig(level=getattr(logging, CoreConfig.LOG_LEVEL))
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management - startup and shutdown"""
    logger.info("Starting AI Dating Agent API...")

    # Initialize database manager
    app.state.db_manager = DatabaseManager()

    # Initialize Redis manager
    app.state.redis_manager = RedisManager()

    # Connect to database with retry mechanism (production standard)
    max_retries = 3
    retry_delay = 5

    # Database connection
    for attempt in range(max_retries):
        try:
            if await app.state.db_manager.connect():
                logger.info("Database connected successfully")
                app.state.db_available = True
                break
        except Exception as e:
            if attempt == max_retries - 1:
                logger.critical(f"Database connection failed after {max_retries} attempts: {e}")
                raise RuntimeError("Cannot start application without database connection")

            logger.warning(f"Database connection attempt {attempt + 1} failed: {e}")
            logger.info(f"Retrying in {retry_delay} seconds...")
            await asyncio.sleep(retry_delay)

    # Redis connection (non-blocking, can degrade gracefully)
    for attempt in range(max_retries):
        try:
            if await app.state.redis_manager.connect():
                logger.info("Redis connected successfully")
                app.state.redis_available = True
                break
        except Exception as e:
            if attempt == max_retries - 1:
                logger.warning(f"Redis connection failed after {max_retries} attempts: {e}")
                logger.info("Application will continue without Redis (degraded mode)")
                app.state.redis_available = False
                break

            logger.warning(f"Redis connection attempt {attempt + 1} failed: {e}")
            logger.info(f"Retrying in {retry_delay} seconds...")
            await asyncio.sleep(retry_delay)

    logger.info("AI Dating Agent API started successfully")

    yield  # Application runs here

    # Cleanup on shutdown
    logger.info("Shutting down AI Dating Agent API...")

    # Disconnect Redis
    if hasattr(app.state, 'redis_manager'):
        await app.state.redis_manager.disconnect()
        logger.info("Redis disconnected")

    # Disconnect Database
    if hasattr(app.state, 'db_manager'):
        await app.state.db_manager.disconnect()
        logger.info("Database disconnected")

    logger.info("AI Dating Agent API shutdown complete")

app = FastAPI(
    title="AI Dating Agent API",
    description="Backend API for AI-powered dating application",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# TODO: Add CORS middleware configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure properly for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# TODO: Add custom middleware
# app.add_middleware(AuthMiddleware)
# app.add_middleware(RateLimitMiddleware)

# Include API routers
app.include_router(auth.router, prefix="/api/v1/auth", tags=["authentication"])
app.include_router(users.router, prefix="/api/v1/users", tags=["users"])
app.include_router(conversations.router, prefix="/api/v1/conversations", tags=["conversations"])
app.include_router(profiles.router, prefix="/api/v1/profiles", tags=["profiles"])
app.include_router(matching.router, prefix="/api/v1/matching", tags=["matching"])

@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "AI Dating Agent API", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    """Basic health check endpoint - fast response for load balancers"""
    return {"status": "ok", "version": "1.0.0"}

@app.get("/health/detailed")
async def detailed_health_check(request: Request):
    """Detailed health check endpoint - comprehensive system status"""
    db_manager = request.app.state.db_manager
    redis_manager = request.app.state.redis_manager

    # Check database status
    db_status = "connected" if db_manager.is_connected else "disconnected"

    # Test database with actual query
    db_query_status = "unknown"
    db_error = None

    if db_manager.is_connected:
        try:
            async with db_manager.get_session() as session:
                from sqlalchemy import text
                await session.execute(text("SELECT 1"))
            db_query_status = "healthy"
        except Exception as e:
            db_query_status = "error"
            db_error = str(e)

    # Check Redis status
    redis_status = "connected" if redis_manager.is_connected else "disconnected"
    redis_ping_status = "unknown"
    redis_error = None
    redis_info = {}

    if redis_manager.is_connected:
        try:
            ping_result = await redis_manager.ping()
            redis_ping_status = "healthy" if ping_result else "error"

            if ping_result:
                redis_info = await redis_manager.get_info()
        except Exception as e:
            redis_ping_status = "error"
            redis_error = str(e)

    # Overall status: healthy if both DB and Redis are healthy (or Redis is gracefully degraded)
    overall_status = "healthy" if db_query_status == "healthy" else "unhealthy"

    response = {
        "status": overall_status,
        "version": "1.0.0",
        "timestamp": datetime.utcnow().isoformat(),
        "services": {
            "api": "running",
            "database": {
                "status": db_status,
                "query_test": db_query_status,
                "error": db_error
            },
            "redis": {
                "status": redis_status,
                "ping_test": redis_ping_status,
                "error": redis_error,
                "info": redis_info
            },
            "ai_agent": "pending"  # TODO: Implement AI agent health check
        }
    }

    return response

@app.get("/health/db")
async def database_health(request: Request):
    """Database-specific health check"""
    db_manager = request.app.state.db_manager

    if not db_manager.is_connected:
        return {
            "status": "disconnected",
            "message": "Database manager not connected",
            "timestamp": datetime.utcnow().isoformat()
        }

    try:
        # Test database connection with simple query
        async with db_manager.get_session() as session:
            from sqlalchemy import text
            result = await session.execute(text("SELECT 1 as test"))
            test_value = result.scalar()

        return {
            "status": "healthy",
            "message": "Database connection active and responsive",
            "test_query": "SELECT 1",
            "test_result": test_value,
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return {
            "status": "error",
            "message": f"Database query failed: {str(e)}",
            "timestamp": datetime.utcnow().isoformat()
        }

@app.get("/health/redis")
async def redis_health(request: Request):
    """Redis-specific health check"""
    redis_manager = request.app.state.redis_manager

    if not redis_manager.is_connected:
        return {
            "status": "disconnected",
            "message": "Redis manager not connected",
            "timestamp": datetime.utcnow().isoformat()
        }

    try:
        # Test Redis connection with ping
        ping_result = await redis_manager.ping()

        if not ping_result:
            return {
                "status": "error",
                "message": "Redis ping failed",
                "timestamp": datetime.utcnow().isoformat()
            }

        # Get Redis server information
        redis_info = await redis_manager.get_info()

        return {
            "status": "healthy",
            "message": "Redis connection active and responsive",
            "ping_result": ping_result,
            "server_info": redis_info,
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        logger.error(f"Redis health check failed: {e}")
        return {
            "status": "error",
            "message": f"Redis operation failed: {str(e)}",
            "timestamp": datetime.utcnow().isoformat()
        }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
