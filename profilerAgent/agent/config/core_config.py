"""
CoreConfig - Core Configuration
DeepSeek API, Database, and core application settings
"""

import os
from typing import Dict, Any, Optional
from dotenv import load_dotenv

# Load .env file - fix path issue
import os.path
env_path = os.path.join(os.path.dirname(__file__), '..', '..', '.env')
load_dotenv(env_path)

class CoreConfig:
    """Core Configuration - MVP Version"""

    # ============ Application Environment ============
    APP_ENV = os.getenv("APP_ENV", "development")
    DEBUG = os.getenv("DEBUG", "true").lower() == "true"
    ENVIRONMENT = os.getenv("ENVIRONMENT", "development")  # development, staging, production
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")

    # ============ DeepSeek AI Configuration ============
    DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY", "")
    DEEPSEEK_BASE_URL = "https://api.deepseek.com"
    DEEPSEEK_MODEL = "deepseek-chat"
    DEEPSEEK_TEMPERATURE = 0.3
    DEEPSEEK_MAX_TOKENS = 2000
    DEEPSEEK_TIMEOUT = 60  # Request timeout in seconds

    # ============ Database Configuration ============
    DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:password@localhost:5432/dating_app")
    REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")

    # Database connection pool settings
    DB_POOL_SIZE = int(os.getenv("DB_POOL_SIZE", "10"))
    DB_MAX_OVERFLOW = int(os.getenv("DB_MAX_OVERFLOW", "20"))
    DB_POOL_TIMEOUT = int(os.getenv("DB_POOL_TIMEOUT", "30"))

    # Redis settings
    REDIS_MAX_CONNECTIONS = int(os.getenv("REDIS_MAX_CONNECTIONS", "10"))
    REDIS_SOCKET_TIMEOUT = int(os.getenv("REDIS_SOCKET_TIMEOUT", "5"))

    # ============ Security Configuration ============
    SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-change-in-production")
    JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY", "your-jwt-secret-key")
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("JWT_ACCESS_TOKEN_EXPIRE_MINUTES", "30"))

    # ============ AI Agent Configuration ============
    # Registration settings
    MAX_REGISTRATION_ATTEMPTS = int(os.getenv("MAX_REGISTRATION_ATTEMPTS", "5"))
    REGISTRATION_TIMEOUT_MINUTES = int(os.getenv("REGISTRATION_TIMEOUT_MINUTES", "30"))

    # Profile analysis settings
    MIN_CONFIDENCE_THRESHOLD = float(os.getenv("MIN_CONFIDENCE_THRESHOLD", "0.6"))
    PROFILE_UPDATE_THRESHOLD = float(os.getenv("PROFILE_UPDATE_THRESHOLD", "0.7"))

    # ============ Configuration Validation ============
    @classmethod
    def validate_config(cls) -> Dict[str, Any]:
        """Validate configuration completeness"""
        errors = []
        warnings = []

        # Required configurations
        if not cls.DEEPSEEK_API_KEY:
            errors.append("DEEPSEEK_API_KEY is required")
        if not cls.DATABASE_URL:
            errors.append("DATABASE_URL is required")
        if not cls.REDIS_URL:
            errors.append("REDIS_URL is required")

        # Security warnings
        if cls.SECRET_KEY == "your-secret-key-change-in-production":
            warnings.append("SECRET_KEY should be changed in production")

        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings
        }

    # ============ Configuration Getters ============
    @classmethod
    def get_deepseek_config(cls) -> Dict[str, Any]:
        """Get DeepSeek AI configuration"""
        return {
            "api_key": cls.DEEPSEEK_API_KEY,
            "base_url": cls.DEEPSEEK_BASE_URL,
            "model": cls.DEEPSEEK_MODEL,
            "temperature": cls.DEEPSEEK_TEMPERATURE,
            "max_tokens": cls.DEEPSEEK_MAX_TOKENS,
            "timeout": cls.DEEPSEEK_TIMEOUT
        }

    @classmethod
    def get_database_config(cls) -> Dict[str, Any]:
        """Get database configuration"""
        return {
            "url": cls.DATABASE_URL,
            "pool_size": cls.DB_POOL_SIZE,
            "max_overflow": cls.DB_MAX_OVERFLOW,
            "pool_timeout": cls.DB_POOL_TIMEOUT
        }

    @classmethod
    def get_redis_config(cls) -> Dict[str, Any]:
        """Get Redis configuration"""
        return {
            "url": cls.REDIS_URL,
            "max_connections": cls.REDIS_MAX_CONNECTIONS,
            "socket_timeout": cls.REDIS_SOCKET_TIMEOUT
        }

    @classmethod
    def get_agent_config(cls) -> Dict[str, Any]:
        """Get AI agent configuration"""
        return {
            "max_registration_attempts": cls.MAX_REGISTRATION_ATTEMPTS,
            "registration_timeout_minutes": cls.REGISTRATION_TIMEOUT_MINUTES,
            "min_confidence_threshold": cls.MIN_CONFIDENCE_THRESHOLD,
            "profile_update_threshold": cls.PROFILE_UPDATE_THRESHOLD
        }

    @classmethod
    def is_production(cls) -> bool:
        """Check if running in production environment"""
        return cls.ENVIRONMENT.lower() == "production"

    @classmethod
    def is_development(cls) -> bool:
        """Check if running in development environment"""
        return cls.ENVIRONMENT.lower() == "development"
