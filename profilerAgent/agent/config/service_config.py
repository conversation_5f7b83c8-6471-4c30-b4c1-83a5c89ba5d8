"""
ServiceConfig - External Services Configuration
Twilio SMS and other external service configurations
"""

import os
from typing import Dict, Any

class ServiceConfig:
    """External Services Configuration - Based on .env file"""

    # ============ Twilio Configuration (Hybrid Mode) ============
    TWILIO_MODE = os.getenv("TWILIO_MODE", "live").lower()  # "test" | "live" | "auto"

    # Live credentials - Real SMS sending
    TWILIO_LIVE_ACCOUNT_SID = os.getenv("TWILIO_LIVE_ACCOUNT_SID", "")
    TWILIO_LIVE_AUTH_TOKEN = os.getenv("TWILIO_LIVE_AUTH_TOKEN", "")

    # Test credentials - Simulation testing
    TWILIO_TEST_ACCOUNT_SID = os.getenv("TWILIO_TEST_ACCOUNT_SID", "")
    TWILIO_TEST_AUTH_TOKEN = os.getenv("TWILIO_TEST_AUTH_TOKEN", "")

    # SMS Service configuration
    TWILIO_SMS_SERVICE_SID = os.getenv("TWILIO_SMS_SERVICE_SID", "")
    TWILIO_SMS_PHONE_NUMBER = os.getenv("TWILIO_SMS_PHONE_NUMBER", "")

    # Voice configuration
    TWILIO_VOICE_PHONE_NUMBER = os.getenv("TWILIO_VOICE_PHONE_NUMBER", "")
    TWILIO_WEBHOOK_URL = os.getenv("TWILIO_WEBHOOK_URL", "")

    # SMS verification settings
    SMS_CODE_EXPIRE_MINUTES = int(os.getenv("SMS_CODE_EXPIRE_MINUTES", "5"))
    SMS_MAX_ATTEMPTS = int(os.getenv("SMS_MAX_ATTEMPTS", "3"))
    SMS_RATE_LIMIT_PER_HOUR = int(os.getenv("SMS_RATE_LIMIT_PER_HOUR", "5"))

    # ============ AI Services Configuration ============
    AI_PROVIDER = os.getenv("AI_PROVIDER", "openai")  # "openai" | "anthropic" | "local" | "deepseek"
    AI_ENABLED = os.getenv("AI_ENABLED", "true").lower() == "true"
    AI_TIMEOUT = int(os.getenv("AI_TIMEOUT", "30"))
    AI_TIMEOUT_SECONDS = int(os.getenv("AI_TIMEOUT_SECONDS", "30"))  # Alias for compatibility

    # OpenAI Configuration
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
    OPENAI_MODEL = os.getenv("OPENAI_MODEL", "gpt-4o-mini")

    # Anthropic Configuration
    ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY", "")
    ANTHROPIC_MODEL = os.getenv("ANTHROPIC_MODEL", "claude-3-sonnet-20240229")

    # DeepSeek Configuration
    DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY", "")
    DEEPSEEK_MODEL = os.getenv("DEEPSEEK_MODEL", "deepseek-chat")

    # AI Service settings
    AI_MAX_TOKENS = int(os.getenv("AI_MAX_TOKENS", "2000"))
    AI_TEMPERATURE = float(os.getenv("AI_TEMPERATURE", "0.7"))

    # ============ Voice Services Configuration ============
    VOICE_LANGUAGE = os.getenv("VOICE_LANGUAGE", "en-US")
    VOICE_MODEL = os.getenv("VOICE_MODEL", "elevenlabs_english")
    ASR_LANGUAGE = os.getenv("ASR_LANGUAGE", "en-US")

    # ============ Frontend Integration Configuration ============
    FRONTEND_URL = os.getenv("FRONTEND_URL", "http://localhost:3000")
    CORS_ORIGINS = os.getenv("CORS_ORIGINS", '["http://localhost:3000", "http://localhost:5173"]')

    # ============ Configuration Validation ============
    @classmethod
    def validate_ai_config(cls) -> Dict[str, Any]:
        """Validate AI service configuration"""
        errors = []
        warnings = []

        provider = cls.AI_PROVIDER.lower()

        if provider == "openai":
            if not cls.OPENAI_API_KEY:
                errors.append("OPENAI_API_KEY is required when AI_PROVIDER=openai")
        elif provider == "anthropic":
            if not cls.ANTHROPIC_API_KEY:
                errors.append("ANTHROPIC_API_KEY is required when AI_PROVIDER=anthropic")
        elif provider == "deepseek":
            if not cls.DEEPSEEK_API_KEY:
                errors.append("DEEPSEEK_API_KEY is required when AI_PROVIDER=deepseek")
        elif provider == "local":
            warnings.append("Local AI provider selected - ensure local model is available")
        else:
            errors.append(f"Unsupported AI_PROVIDER: {provider}. Use 'openai', 'anthropic', 'deepseek', or 'local'")

        if not cls.AI_ENABLED:
            warnings.append("AI services are disabled (AI_ENABLED=false)")

        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings
        }

    @classmethod
    def validate_twilio_config(cls) -> Dict[str, Any]:
        """Validate Twilio configuration based on mode"""
        errors = []
        warnings = []

        # Get current credentials based on mode
        credentials = cls.get_twilio_credentials()

        if not credentials["account_sid"]:
            errors.append(f"TWILIO_{cls.TWILIO_MODE.upper()}_ACCOUNT_SID is required")
        if not credentials["auth_token"]:
            errors.append(f"TWILIO_{cls.TWILIO_MODE.upper()}_AUTH_TOKEN is required")

        # SMS service validation
        if not cls.TWILIO_SMS_SERVICE_SID and not cls.TWILIO_SMS_PHONE_NUMBER:
            warnings.append("Either TWILIO_SMS_SERVICE_SID or TWILIO_SMS_PHONE_NUMBER is recommended")

        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings
        }

    # ============ Configuration Getters ============
    @classmethod
    def get_twilio_credentials(cls) -> Dict[str, Any]:
        """Get Twilio credentials based on mode"""
        mode = cls.TWILIO_MODE.lower()

        if mode == "live":
            return {
                "account_sid": cls.TWILIO_LIVE_ACCOUNT_SID,
                "auth_token": cls.TWILIO_LIVE_AUTH_TOKEN,
                "mode": "live"
            }
        elif mode == "test":
            return {
                "account_sid": cls.TWILIO_TEST_ACCOUNT_SID,
                "auth_token": cls.TWILIO_TEST_AUTH_TOKEN,
                "mode": "test"
            }
        elif mode == "auto":
            # Auto mode: test for development, live for production
            from .core_config import CoreConfig
            if CoreConfig.is_production():
                return {
                    "account_sid": cls.TWILIO_LIVE_ACCOUNT_SID,
                    "auth_token": cls.TWILIO_LIVE_AUTH_TOKEN,
                    "mode": "live"
                }
            else:
                return {
                    "account_sid": cls.TWILIO_TEST_ACCOUNT_SID,
                    "auth_token": cls.TWILIO_TEST_AUTH_TOKEN,
                    "mode": "test"
                }
        else:
            # Default to test mode
            return {
                "account_sid": cls.TWILIO_TEST_ACCOUNT_SID,
                "auth_token": cls.TWILIO_TEST_AUTH_TOKEN,
                "mode": "test"
            }

    @classmethod
    def get_twilio_config(cls) -> Dict[str, Any]:
        """Get complete Twilio configuration"""
        credentials = cls.get_twilio_credentials()
        return {
            **credentials,
            "sms_service_sid": cls.TWILIO_SMS_SERVICE_SID,
            "sms_phone_number": cls.TWILIO_SMS_PHONE_NUMBER,
            "voice_phone_number": cls.TWILIO_VOICE_PHONE_NUMBER,
            "webhook_url": cls.TWILIO_WEBHOOK_URL,
            "code_expire_minutes": cls.SMS_CODE_EXPIRE_MINUTES,
            "max_attempts": cls.SMS_MAX_ATTEMPTS,
            "rate_limit_per_hour": cls.SMS_RATE_LIMIT_PER_HOUR
        }

    @classmethod
    def get_voice_config(cls) -> Dict[str, Any]:
        """Get voice services configuration"""
        return {
            "language": cls.VOICE_LANGUAGE,
            "model": cls.VOICE_MODEL,
            "asr_language": cls.ASR_LANGUAGE
        }

    @classmethod
    def get_ai_config(cls) -> Dict[str, Any]:
        """Get AI service configuration"""
        provider = cls.AI_PROVIDER.lower()

        config = {
            "provider": provider,
            "enabled": cls.AI_ENABLED,
            "max_tokens": cls.AI_MAX_TOKENS,
            "temperature": cls.AI_TEMPERATURE,
            "timeout": cls.AI_TIMEOUT,
            "timeout_seconds": cls.AI_TIMEOUT_SECONDS
        }

        if provider == "openai":
            config.update({
                "api_key": cls.OPENAI_API_KEY,
                "model": cls.OPENAI_MODEL
            })
        elif provider == "anthropic":
            config.update({
                "api_key": cls.ANTHROPIC_API_KEY,
                "model": cls.ANTHROPIC_MODEL
            })
        elif provider == "deepseek":
            config.update({
                "api_key": cls.DEEPSEEK_API_KEY,
                "model": cls.DEEPSEEK_MODEL
            })
        elif provider == "local":
            config.update({
                "model": "local-model",
                "endpoint": "http://localhost:8080"  # Default local endpoint
            })

        return config

    @classmethod
    def get_frontend_config(cls) -> Dict[str, Any]:
        """Get frontend integration configuration"""
        import json
        try:
            cors_origins = json.loads(cls.CORS_ORIGINS)
        except (json.JSONDecodeError, TypeError):
            cors_origins = ["http://localhost:3000", "http://localhost:5173"]

        return {
            "frontend_url": cls.FRONTEND_URL,
            "cors_origins": cors_origins
        }


