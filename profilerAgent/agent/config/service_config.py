"""
ServiceConfig - External Services Configuration
Twilio SMS and other external service configurations
"""

import os
from typing import Dict, Any

class ServiceConfig:
    """External Services Configuration - MVP Version"""

    # ============ Twilio SMS Configuration ============
    TWILIO_ACCOUNT_SID = os.getenv("TWILIO_ACCOUNT_SID", "")
    TWILIO_AUTH_TOKEN = os.getenv("TWILIO_AUTH_TOKEN", "")
    TWILIO_PHONE_NUMBER = os.getenv("TWILIO_PHONE_NUMBER", "")

    # SMS verification settings
    SMS_CODE_EXPIRE_MINUTES = int(os.getenv("SMS_CODE_EXPIRE_MINUTES", "5"))
    SMS_MAX_ATTEMPTS = int(os.getenv("SMS_MAX_ATTEMPTS", "3"))
    SMS_RATE_LIMIT_PER_HOUR = int(os.getenv("SMS_RATE_LIMIT_PER_HOUR", "5"))

    # ============ Configuration Validation ============
    @classmethod
    def validate_twilio_config(cls) -> Dict[str, Any]:
        """Validate Twilio SMS configuration"""
        errors = []
        warnings = []

        if not cls.TWILIO_ACCOUNT_SID:
            errors.append("TWILIO_ACCOUNT_SID is required for SMS verification")
        if not cls.TWILIO_AUTH_TOKEN:
            errors.append("TWILIO_AUTH_TOKEN is required for SMS verification")
        if not cls.TWILIO_PHONE_NUMBER:
            warnings.append("TWILIO_PHONE_NUMBER is recommended for SMS sending")

        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings
        }

    # ============ Configuration Getters ============
    @classmethod
    def get_twilio_config(cls) -> Dict[str, Any]:
        """Get Twilio SMS configuration"""
        return {
            "account_sid": cls.TWILIO_ACCOUNT_SID,
            "auth_token": cls.TWILIO_AUTH_TOKEN,
            "phone_number": cls.TWILIO_PHONE_NUMBER,
            "code_expire_minutes": cls.SMS_CODE_EXPIRE_MINUTES,
            "max_attempts": cls.SMS_MAX_ATTEMPTS,
            "rate_limit_per_hour": cls.SMS_RATE_LIMIT_PER_HOUR
        }


