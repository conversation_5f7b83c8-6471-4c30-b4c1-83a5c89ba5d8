"""
Configuration module for AI Dating Agent MVP
Contains all configuration settings
"""

from typing import Dict, Any
from .core_config import CoreConfig
from .service_config import ServiceConfig

__all__ = [
    'CoreConfig',
    'ServiceConfig'
]

# Utility functions
def validate_all_configs() -> Dict[str, Any]:
    """Validate all configurations"""

    # Validate core configuration
    core_validation = CoreConfig.validate_config()

    # Validate service configuration (optional)
    service_validation = ServiceConfig.validate_twilio_config()

    # Combine results
    all_errors = core_validation["errors"] + service_validation["errors"]
    all_warnings = core_validation["warnings"] + service_validation["warnings"]

    return {
        "valid": len(all_errors) == 0,
        "errors": all_errors,
        "warnings": all_warnings,
        "core": core_validation,
        "services": service_validation
    }

def get_environment_info() -> Dict[str, Any]:
    """Get environment information"""
    return {
        "environment": CoreConfig.ENVIRONMENT,
        "debug": CoreConfig.DEBUG,
        "is_production": CoreConfig.is_production(),
        "is_development": CoreConfig.is_development()
    }
