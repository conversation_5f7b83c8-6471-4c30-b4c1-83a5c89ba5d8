"""
AI Service - 统一AI服务
支持多种AI提供商：OpenAI、Anthropic、DeepSeek、Local
"""

import asyncio
import aiohttp
import logging
from typing import Optional, Dict, Any, List
from ..config.service_config import ServiceConfig

logger = logging.getLogger(__name__)

# 避免循环导入，在运行时导入ConversationResponse
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .regular_conversation_agent import ConversationResponse

class AIService:
    """统一AI服务 - 支持多种AI提供商"""

    def __init__(self):
        """初始化AI服务"""
        self.provider = ServiceConfig.AI_PROVIDER.lower()
        self.timeout = ServiceConfig.AI_TIMEOUT
        self.enabled = ServiceConfig.AI_ENABLED
        self.max_tokens = ServiceConfig.AI_MAX_TOKENS
        self.temperature = ServiceConfig.AI_TEMPERATURE

        # 根据提供商配置API
        self._configure_provider()

        logger.info(f"AIService initialized - provider: {self.provider}, enabled: {self.enabled}")

    def _configure_provider(self):
        """配置AI提供商"""
        if self.provider == "openai":
            self.api_key = ServiceConfig.OPENAI_API_KEY
            self.api_url = "https://api.openai.com/v1/chat/completions"
            self.model = ServiceConfig.OPENAI_MODEL
            if not self.api_key:
                logger.warning("OpenAI API key not configured, AI features disabled")
                self.enabled = False

        elif self.provider == "anthropic":
            self.api_key = ServiceConfig.ANTHROPIC_API_KEY
            self.api_url = "https://api.anthropic.com/v1/messages"
            self.model = ServiceConfig.ANTHROPIC_MODEL
            if not self.api_key:
                logger.warning("Anthropic API key not configured, AI features disabled")
                self.enabled = False

        elif self.provider == "deepseek":
            self.api_key = ServiceConfig.DEEPSEEK_API_KEY
            self.api_url = "https://api.deepseek.com/v1/chat/completions"
            self.model = ServiceConfig.DEEPSEEK_MODEL
            if not self.api_key:
                logger.warning("DeepSeek API key not configured, AI features disabled")
                self.enabled = False

        elif self.provider == "local":
            self.api_key = "local"
            self.api_url = "http://localhost:8080/v1/chat/completions"
            self.model = "local-model"
            logger.info("Using local AI model")

        else:
            logger.error(f"Unsupported AI provider: {self.provider}")
            self.enabled = False

    async def generate_text(self, prompt: str, system_prompt: str = None) -> str:
        """
        生成文本 - 单轮对话

        Args:
            prompt: 用户提示
            system_prompt: 系统提示（可选）

        Returns:
            生成的文本字符串
        """
        if not self.enabled:
            logger.debug("AI service disabled, returning default response")
            return "I'm here to help! How can I assist you today?"

        try:
            # 构建消息
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": prompt})

            # 调用chat方法
            response = await self.chat(messages)
            return response.content

        except Exception as e:
            logger.error(f"Failed to generate text: {e}")
            return "I'm sorry, I'm having trouble responding right now. Please try again."

    async def chat(self, messages: List[Dict[str, str]]) -> 'ConversationResponse':
        """
        多轮对话

        Args:
            messages: 消息列表，格式为 [{"role": "user/assistant/system", "content": "..."}]

        Returns:
            ConversationResponse对象
        """
        if not self.enabled:
            logger.debug("AI service disabled, returning default response")
            # 动态导入避免循环导入
            from .regular_conversation_agent import ConversationResponse
            return ConversationResponse(
                content="I'm here to help! How can I assist you today?"
            )

        try:
            # 调用对应的AI API
            if self.provider in ["openai", "deepseek", "local"]:
                content = await self._call_openai_compatible_api(messages)
            elif self.provider == "anthropic":
                content = await self._call_anthropic_api(messages)
            else:
                raise Exception(f"Unsupported provider: {self.provider}")

            # 动态导入避免循环导入
            from .regular_conversation_agent import ConversationResponse
            return ConversationResponse(content=content)

        except Exception as e:
            logger.error(f"Failed to process chat: {e}")
            # 动态导入避免循环导入
            from .regular_conversation_agent import ConversationResponse
            return ConversationResponse(
                content="Sorry, I didn't catch that. Could you say that again?"
            )

    async def _call_openai_compatible_api(self, messages: List[Dict[str, str]]) -> str:
        """调用OpenAI兼容的API (OpenAI, DeepSeek, Local)"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        payload = {
            "model": self.model,
            "messages": messages,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature
        }

        timeout = aiohttp.ClientTimeout(total=self.timeout)
        connector = aiohttp.TCPConnector(ssl=False if self.provider == "local" else True)

        try:
            async with aiohttp.ClientSession(timeout=timeout, connector=connector) as session:
                async with session.post(self.api_url, json=payload, headers=headers) as response:
                    logger.debug(f"API response status: {response.status}")

                    if response.status == 200:
                        data = await response.json()
                        content = data["choices"][0]["message"]["content"].strip()
                        logger.debug(f"API returned content: {content[:100]}...")
                        return content
                    else:
                        response_text = await response.text()
                        logger.error(f"{self.provider.upper()} API error {response.status}: {response_text}")
                        raise Exception(f"API error {response.status}")

        except asyncio.TimeoutError:
            logger.error(f"{self.provider.upper()} API timeout")
            raise Exception("API timeout")
        except Exception as e:
            logger.error(f"{self.provider.upper()} API call failed: {e}")
            raise

    async def _call_anthropic_api(self, messages: List[Dict[str, str]]) -> str:
        """调用Anthropic Claude API"""
        headers = {
            "x-api-key": self.api_key,
            "Content-Type": "application/json",
            "anthropic-version": "2023-06-01"
        }

        # 转换消息格式
        system_message = ""
        claude_messages = []

        for msg in messages:
            if msg["role"] == "system":
                system_message = msg["content"]
            else:
                claude_messages.append({
                    "role": msg["role"],
                    "content": msg["content"]
                })

        payload = {
            "model": self.model,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "messages": claude_messages
        }

        if system_message:
            payload["system"] = system_message

        timeout = aiohttp.ClientTimeout(total=self.timeout)

        try:
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(self.api_url, json=payload, headers=headers) as response:
                    logger.debug(f"Anthropic API response status: {response.status}")

                    if response.status == 200:
                        data = await response.json()
                        content = data["content"][0]["text"].strip()
                        logger.debug(f"Anthropic API returned content: {content[:100]}...")
                        return content
                    else:
                        response_text = await response.text()
                        logger.error(f"Anthropic API error {response.status}: {response_text}")
                        raise Exception(f"Anthropic API error {response.status}")

        except asyncio.TimeoutError:
            logger.error("Anthropic API timeout")
            raise Exception("Anthropic API timeout")
        except Exception as e:
            logger.error(f"Anthropic API call failed: {e}")
            raise

    async def generate_simple_question(self, domain: str, user_response: str) -> Optional[str]:
        """
        生成简单的后续问题 - 保持向后兼容

        Args:
            domain: 当前领域 (professional/personality/interests/relationships)
            user_response: 用户的回答

        Returns:
            生成的问题字符串，失败时返回None
        """
        if not self.enabled:
            logger.debug("AI service disabled, returning None")
            return None

        try:
            logger.info(f"Generating AI question for domain: {domain}")
            logger.debug(f"User response: {user_response}")

            # 构建简单的prompt
            prompt = self._build_simple_prompt(domain, user_response)

            # 使用generate_text方法
            question = await self.generate_text(prompt)

            if question:
                # 清理AI响应格式
                question = self._clean_ai_response(question)

                # 自动添加问号如果缺失
                if not question.strip().endswith('?'):
                    question = question.strip() + '?'

                if self._validate_question(question):
                    logger.info(f"AI question generated: {question}")
                    return question
                else:
                    logger.warning(f"AI question validation failed: {question}")
                    return None
            else:
                logger.warning("AI returned empty question")
                return None

        except Exception as e:
            logger.error(f"AI question generation failed: {e}")
            return None

    def _build_simple_prompt(self, domain: str, user_response: str) -> str:
        """构建简单的问题生成prompt"""
        domain_prompts = {
            "professional": "Generate a follow-up question about their work or career based on their response.",
            "personality": "Generate a follow-up question about their personality or character based on their response.",
            "interests": "Generate a follow-up question about their hobbies or interests based on their response.",
            "relationships": "Generate a follow-up question about their relationship preferences based on their response."
        }

        base_prompt = domain_prompts.get(domain, "Generate a natural follow-up question based on their response.")

        return f"""
{base_prompt}

User's response: "{user_response}"

Requirements:
1. Keep it conversational and natural
2. Ask only ONE question
3. Make it engaging and relevant
4. Avoid sensitive topics
5. Maximum 20 words

Generate only the question, nothing else.
"""

    def _clean_ai_response(self, response: str) -> str:
        """清理AI响应"""
        # 移除引号
        cleaned = response.strip().strip('"').strip("'")

        # 移除常见的前缀
        prefixes_to_remove = [
            "Question:", "Q:", "Follow-up:", "Next question:",
            "Here's a question:", "How about:", "What about:"
        ]

        for prefix in prefixes_to_remove:
            if cleaned.startswith(prefix):
                cleaned = cleaned[len(prefix):].strip()

        # 移除括号中的解释文本
        import re
        cleaned = re.sub(r'\s*\([^)]*words[^)]*\)\s*', '', cleaned)

        # 确保只有一个问号在末尾
        cleaned = cleaned.rstrip('?')
        if not cleaned.endswith('?'):
            cleaned += '?'

        return cleaned.strip()

    def _validate_question(self, question: str) -> bool:
        """验证问题质量"""
        if not question or len(question.strip()) < 5:
            return False

        if len(question) > 200:  # 太长
            return False

        if self._contains_sensitive_words(question):
            return False

        return True

    def _contains_sensitive_words(self, question: str) -> bool:
        """检查是否包含敏感词"""
        import re

        sensitive_words = [
            'sex', 'sexual', 'money', 'salary', 'income', 'politics',
            'religion', 'weight', 'divorce', 'ex-wife', 'ex-husband',
            'depression', 'anxiety', 'therapy', 'medication'
        ]

        question_lower = question.lower()
        for word in sensitive_words:
            if re.search(r'\b' + re.escape(word) + r'\b', question_lower):
                return True
        return False
