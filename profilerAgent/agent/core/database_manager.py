"""
DatabaseManager - 简化的数据库管理器
专注于注册流程和用户档案管理
"""

import json
import logging
import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime

# 导入配置
from ..config.core_config import CoreConfig

logger = logging.getLogger(__name__)

class DatabaseManager:
    """简化的异步数据库管理器"""

    def __init__(self):
        """初始化数据库管理器"""
        self.is_connected = False
        self.engine = None
        self.session_factory = None

        # 获取数据库配置
        self.db_config = CoreConfig.get_database_config()

        # 内存存储备用 (开发/测试用)
        self._memory_store = {
            'users': {},
            'user_profiles': {},
            'conversations': {},
            'conversation_messages': {},
            'user_context_cache': {},
            'voice_sessions': {}
        }

        logger.info("DatabaseManager initialized with CoreConfig")

    async def connect(self) -> bool:
        """连接数据库"""
        try:
            # 尝试导入数据库依赖
            try:
                from sqlalchemy.ext.asyncio import create_async_engine, async_sessionmaker
                from sqlalchemy import text
            except ImportError:
                logger.warning("SQLAlchemy not available, using memory store")
                self.is_connected = False
                return False

            # 创建异步引擎
            self.engine = create_async_engine(
                self.db_config["url"],
                pool_size=self.db_config["pool_size"],
                max_overflow=self.db_config["max_overflow"],
                pool_timeout=self.db_config["pool_timeout"],
                echo=CoreConfig.DEBUG
            )

            # 创建会话工厂
            self.session_factory = async_sessionmaker(
                self.engine,
                expire_on_commit=False
            )

            # 测试连接
            async with self.session_factory() as session:
                await session.execute(text("SELECT 1"))
                await session.commit()

            self.is_connected = True
            logger.info("Database connection established using CoreConfig")
            return True

        except Exception as e:
            logger.error(f"Database connection error: {e}")
            logger.info("Using memory store as fallback")
            self.is_connected = False
            return False

    async def disconnect(self):
        """断开数据库连接"""
        try:
            if self.engine:
                await self.engine.dispose()
                self.engine = None
                self.session_factory = None
                self.is_connected = False
                logger.info("Database connection closed")
        except Exception as e:
            logger.error(f"Error closing database connection: {e}")

    def get_session(self):
        """获取数据库会话"""
        if not self.is_connected or not self.session_factory:
            raise RuntimeError("Database not connected")
        return self.session_factory()

    # ============ 用户管理 ============
    
    async def create_user(self, user_id: str, phone_number: str, name: str = None) -> bool:
        """创建新用户"""
        try:
            if self.is_connected:
                from sqlalchemy import text

                async with self.get_session() as session:
                    sql = text("""
                        INSERT INTO users (user_id, phone_number, name, status, created_at, updated_at)
                        VALUES (:user_id, :phone_number, :name, 'pending', NOW(), NOW())
                        ON CONFLICT (user_id) DO UPDATE SET
                            phone_number = EXCLUDED.phone_number,
                            name = EXCLUDED.name,
                            updated_at = NOW()
                    """)
                    await session.execute(sql, {
                        "user_id": user_id,
                        "phone_number": phone_number,
                        "name": name
                    })
                    await session.commit()
            else:
                # 内存存储
                self._memory_store['users'][user_id] = {
                    'user_id': user_id,
                    'phone_number': phone_number,
                    'name': name,
                    'status': 'pending',
                    'created_at': datetime.now(),
                    'updated_at': datetime.now()
                }
            
            logger.info(f"Created user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create user {user_id}: {e}")
            return False
    
    async def update_user_status(self, user_id: str, status: str) -> bool:
        """更新用户状态"""
        try:
            if self.is_connected:
                from sqlalchemy import text

                async with self.get_session() as session:
                    sql = text("""
                        UPDATE users 
                        SET status = :status, updated_at = NOW()
                        WHERE user_id = :user_id
                    """)
                    result = await session.execute(sql, {
                        "user_id": user_id,
                        "status": status
                    })
                    await session.commit()
                    return result.rowcount > 0
            else:
                # 内存存储
                if user_id in self._memory_store['users']:
                    self._memory_store['users'][user_id]['status'] = status
                    self._memory_store['users'][user_id]['updated_at'] = datetime.now()
                    return True
                return False
                
        except Exception as e:
            logger.error(f"Failed to update user status {user_id}: {e}")
            return False
    
    async def get_user(self, user_id: str) -> Optional[Dict[str, Any]]:
        """获取用户信息"""
        try:
            if self.is_connected:
                from sqlalchemy import text

                async with self.get_session() as session:
                    sql = text("SELECT * FROM users WHERE user_id = :user_id")
                    result = await session.execute(sql, {"user_id": user_id})
                    row = result.fetchone()
                    if row:
                        return dict(row._mapping)
                    return None
            else:
                # 内存存储
                return self._memory_store['users'].get(user_id)
                
        except Exception as e:
            logger.error(f"Failed to get user {user_id}: {e}")
            return None
    
    # ============ 用户档案管理 ============
    
    async def create_user_profile(self, user_id: str, profile_data: Dict[str, Any] = None) -> bool:
        """创建用户档案"""
        try:
            if profile_data is None:
                profile_data = {}
            
            if self.is_connected:
                from sqlalchemy import text

                async with self.get_session() as session:
                    sql = text("""
                        INSERT INTO user_profiles (user_id, profile_data, confidence_scores, last_updated, conversation_count)
                        VALUES (:user_id, :profile_data, '{}', NOW(), 0)
                        ON CONFLICT (user_id) DO UPDATE SET
                            profile_data = EXCLUDED.profile_data,
                            last_updated = NOW()
                    """)
                    await session.execute(sql, {
                        "user_id": user_id,
                        "profile_data": json.dumps(profile_data)
                    })
                    await session.commit()
            else:
                # 内存存储
                self._memory_store['user_profiles'][user_id] = {
                    'user_id': user_id,
                    'profile_data': profile_data,
                    'confidence_scores': {},
                    'last_updated': datetime.now(),
                    'conversation_count': 0
                }
            
            logger.info(f"Created profile for user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create profile for user {user_id}: {e}")
            return False
    
    async def get_user_profile(self, user_id: str) -> Optional[Dict[str, Any]]:
        """获取用户档案"""
        try:
            if self.is_connected:
                from sqlalchemy import text

                async with self.get_session() as session:
                    sql = text("SELECT * FROM user_profiles WHERE user_id = :user_id")
                    result = await session.execute(sql, {"user_id": user_id})
                    row = result.fetchone()
                    if row:
                        data = dict(row._mapping)
                        # 解析JSON字段
                        if isinstance(data.get('profile_data'), str):
                            data['profile_data'] = json.loads(data['profile_data'])
                        if isinstance(data.get('confidence_scores'), str):
                            data['confidence_scores'] = json.loads(data['confidence_scores'])
                        return data
                    return None
            else:
                # 内存存储
                return self._memory_store['user_profiles'].get(user_id)
                
        except Exception as e:
            logger.error(f"Failed to get profile for user {user_id}: {e}")
            return None
    
    async def update_user_profile(self, user_id: str, profile_data: Dict[str, Any], 
                                confidence_scores: Dict[str, float] = None) -> bool:
        """更新用户档案"""
        try:
            if confidence_scores is None:
                confidence_scores = {}
            
            if self.is_connected:
                from sqlalchemy import text

                async with self.get_session() as session:
                    sql = text("""
                        UPDATE user_profiles 
                        SET profile_data = :profile_data,
                            confidence_scores = :confidence_scores,
                            last_updated = NOW(),
                            conversation_count = conversation_count + 1
                        WHERE user_id = :user_id
                    """)
                    result = await session.execute(sql, {
                        "user_id": user_id,
                        "profile_data": json.dumps(profile_data),
                        "confidence_scores": json.dumps(confidence_scores)
                    })
                    await session.commit()
                    return result.rowcount > 0
            else:
                # 内存存储
                if user_id in self._memory_store['user_profiles']:
                    profile = self._memory_store['user_profiles'][user_id]
                    profile['profile_data'] = profile_data
                    profile['confidence_scores'] = confidence_scores
                    profile['last_updated'] = datetime.now()
                    profile['conversation_count'] += 1
                    return True
                return False
                
        except Exception as e:
            logger.error(f"Failed to update profile for user {user_id}: {e}")
            return False
    
    # ============ 对话管理 ============
    
    async def create_conversation(self, user_id: str, conversation_type: str) -> Optional[str]:
        """创建新对话"""
        try:
            conversation_id = str(uuid.uuid4())
            
            if self.is_connected:
                from sqlalchemy import text

                async with self.get_session() as session:
                    sql = text("""
                        INSERT INTO conversations (id, user_id, type, status, started_at)
                        VALUES (:id, :user_id, :type, 'active', NOW())
                    """)
                    await session.execute(sql, {
                        "id": conversation_id,
                        "user_id": user_id,
                        "type": conversation_type
                    })
                    await session.commit()
            else:
                # 内存存储
                self._memory_store['conversations'][conversation_id] = {
                    'id': conversation_id,
                    'user_id': user_id,
                    'type': conversation_type,
                    'status': 'active',
                    'started_at': datetime.now()
                }
            
            logger.info(f"Created conversation {conversation_id} for user {user_id}")
            return conversation_id
            
        except Exception as e:
            logger.error(f"Failed to create conversation for user {user_id}: {e}")
            return None
    
    async def add_message(self, conversation_id: str, role: str, content: str, 
                         metadata: Dict[str, Any] = None) -> bool:
        """添加消息到对话"""
        try:
            message_id = str(uuid.uuid4())
            if metadata is None:
                metadata = {}
            
            if self.is_connected:
                from sqlalchemy import text

                async with self.get_session() as session:
                    sql = text("""
                        INSERT INTO conversation_messages (id, conversation_id, role, content, metadata, timestamp)
                        VALUES (:id, :conversation_id, :role, :content, :metadata, NOW())
                    """)
                    await session.execute(sql, {
                        "id": message_id,
                        "conversation_id": conversation_id,
                        "role": role,
                        "content": content,
                        "metadata": json.dumps(metadata)
                    })
                    await session.commit()
            else:
                # 内存存储
                if conversation_id not in self._memory_store['conversation_messages']:
                    self._memory_store['conversation_messages'][conversation_id] = []
                
                self._memory_store['conversation_messages'][conversation_id].append({
                    'id': message_id,
                    'conversation_id': conversation_id,
                    'role': role,
                    'content': content,
                    'metadata': metadata,
                    'timestamp': datetime.now()
                })
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to add message to conversation {conversation_id}: {e}")
            return False
    
    async def get_recent_messages(self, conversation_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最近的消息"""
        try:
            if self.is_connected:
                from sqlalchemy import text

                async with self.get_session() as session:
                    sql = text("""
                        SELECT * FROM conversation_messages 
                        WHERE conversation_id = :conversation_id 
                        ORDER BY timestamp DESC 
                        LIMIT :limit
                    """)
                    result = await session.execute(sql, {
                        "conversation_id": conversation_id,
                        "limit": limit
                    })
                    rows = result.fetchall()
                    messages = []
                    for row in reversed(rows):  # 反转以获得正确的时间顺序
                        data = dict(row._mapping)
                        if isinstance(data.get('metadata'), str):
                            data['metadata'] = json.loads(data['metadata'])
                        messages.append(data)
                    return messages
            else:
                # 内存存储
                messages = self._memory_store['conversation_messages'].get(conversation_id, [])
                return messages[-limit:] if messages else []
                
        except Exception as e:
            logger.error(f"Failed to get messages for conversation {conversation_id}: {e}")
            return []

    async def save_conversation_message(self, message_data: Dict[str, Any]) -> bool:
        """Save conversation message"""
        try:
            message_id = str(uuid.uuid4())
            user_id = message_data.get('user_id')
            user_message = message_data.get('user_message', '')
            ai_response = message_data.get('ai_response', '')
            timestamp = message_data.get('timestamp', datetime.now())
            agent_type = message_data.get('agent_type', 'conversation')
            updated_info = message_data.get('updated_info', {})

            if self.is_connected:
                from sqlalchemy import text

                async with self.get_session() as session:
                    # First, try to get existing conversation or create new one
                    conversation_id = str(uuid.uuid4())
                    try:
                        conversation_sql = text("""
                            INSERT INTO conversations (id, user_id, type, status, started_at)
                            VALUES (:id, :user_id, 'chat', 'active', NOW())
                        """)
                        await session.execute(conversation_sql, {
                            "id": conversation_id,
                            "user_id": user_id
                        })
                    except Exception:
                        # If conversation already exists, just use a new ID for this message
                        conversation_id = str(uuid.uuid4())

                    # Save the message
                    message_sql = text("""
                        INSERT INTO conversation_messages
                        (id, conversation_id, user_id, user_message, ai_response, timestamp, metadata)
                        VALUES (:id, :conversation_id, :user_id, :user_message, :ai_response, :timestamp, :metadata)
                    """)
                    await session.execute(message_sql, {
                        "id": message_id,
                        "conversation_id": conversation_id,
                        "user_id": user_id,
                        "user_message": user_message,
                        "ai_response": ai_response,
                        "timestamp": timestamp,
                        "metadata": json.dumps({
                            "agent_type": agent_type,
                            "updated_info": updated_info
                        })
                    })
                    await session.commit()
            else:
                # Memory storage
                if user_id not in self._memory_store['conversation_messages']:
                    self._memory_store['conversation_messages'][user_id] = []

                self._memory_store['conversation_messages'][user_id].append({
                    'id': message_id,
                    'user_id': user_id,
                    'user_message': user_message,
                    'ai_response': ai_response,
                    'timestamp': timestamp,
                    'agent_type': agent_type,
                    'updated_info': updated_info
                })

            logger.debug(f"Saved conversation message for user {user_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to save conversation message: {e}")
            return False

    async def get_conversation_messages(self, user_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get conversation messages for a user"""
        try:
            if self.is_connected:
                from sqlalchemy import text

                async with self.get_session() as session:
                    sql = text("""
                        SELECT cm.*, c.type as conversation_type
                        FROM conversation_messages cm
                        JOIN conversations c ON cm.conversation_id = c.id
                        WHERE c.user_id = :user_id
                        ORDER BY cm.timestamp DESC
                        LIMIT :limit
                    """)
                    result = await session.execute(sql, {
                        "user_id": user_id,
                        "limit": limit
                    })
                    rows = result.fetchall()
                    messages = []
                    for row in reversed(rows):  # Reverse to get chronological order
                        data = dict(row._mapping)
                        if isinstance(data.get('metadata'), str):
                            try:
                                data['metadata'] = json.loads(data['metadata'])
                            except:
                                data['metadata'] = {}
                        messages.append(data)
                    return messages
            else:
                # Memory storage
                messages = self._memory_store['conversation_messages'].get(user_id, [])
                return messages[-limit:] if messages else []

        except Exception as e:
            logger.error(f"Failed to get conversation messages for user {user_id}: {e}")
            return []

    async def disconnect(self):
        """Disconnect from database"""
        try:
            if self.is_connected:
                # Close any active connections
                logger.info("Disconnecting from database")
                self.is_connected = False

            # Clear memory store if needed
            # self._memory_store.clear()  # Uncomment if you want to clear memory on disconnect

            logger.info("Database disconnection completed")

        except Exception as e:
            logger.error(f"Database disconnection error: {e}")

    def check_connection(self) -> Dict[str, Any]:
        """Check database connection status"""
        try:
            return {
                "status": "connected" if self.is_connected else "disconnected",
                "using_memory_store": not self.is_connected,
                "memory_store_stats": {
                    "users": len(self._memory_store.get('users', {})),
                    "profiles": len(self._memory_store.get('user_profiles', {})),
                    "conversations": len(self._memory_store.get('conversations', {})),
                    "messages": sum(len(msgs) for msgs in self._memory_store.get('conversation_messages', {}).values())
                } if not self.is_connected else None
            }
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }

    # ============ Registration State Management ============

    async def get_user_registration_state(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get user registration state from database"""
        try:
            if self.is_connected:
                try:
                    from sqlalchemy import text

                    async with self.get_session() as session:
                        sql = text("""
                            SELECT registration_data FROM user_registration_states
                            WHERE user_id = :user_id
                        """)
                        result = await session.execute(sql, {"user_id": user_id})
                        row = result.fetchone()

                        if row:
                            registration_data = row[0]
                            if isinstance(registration_data, str):
                                return json.loads(registration_data)
                            return registration_data
                except Exception as db_error:
                    logger.warning(f"Database table not available, using memory storage: {db_error}")
                    # Fall through to memory storage

            # Memory storage fallback
            return self._memory_store.get('registration_states', {}).get(user_id)

        except Exception as e:
            logger.error(f"Failed to get registration state for user {user_id}: {e}")
            return None

    async def save_user_registration_state(self, user_id: str, registration_data: Dict[str, Any]) -> bool:
        """Save user registration state to database"""
        try:
            if self.is_connected:
                from sqlalchemy import text

                async with self.get_session() as session:
                    sql = text("""
                        INSERT INTO user_registration_states (user_id, registration_data, updated_at)
                        VALUES (:user_id, :registration_data, NOW())
                        ON CONFLICT (user_id) DO UPDATE SET
                            registration_data = EXCLUDED.registration_data,
                            updated_at = NOW()
                    """)
                    await session.execute(sql, {
                        "user_id": user_id,
                        "registration_data": json.dumps(registration_data)
                    })
                    await session.commit()
            else:
                # Memory storage fallback
                if 'registration_states' not in self._memory_store:
                    self._memory_store['registration_states'] = {}
                self._memory_store['registration_states'][user_id] = registration_data

            logger.debug(f"Saved registration state for user {user_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to save registration state for user {user_id}: {e}")
            return False

    async def cleanup_old_registration_states(self, days_old: int) -> int:
        """Clean up old incomplete registration states"""
        try:
            cleanup_count = 0

            if self.is_connected:
                from sqlalchemy import text

                async with self.get_session() as session:
                    sql = text("""
                        DELETE FROM user_registration_states
                        WHERE updated_at < NOW() - INTERVAL ':days days'
                        AND (registration_data->>'status') != 'registered'
                    """)
                    result = await session.execute(sql, {"days": days_old})
                    cleanup_count = result.rowcount
                    await session.commit()
            else:
                # Memory storage cleanup
                if 'registration_states' in self._memory_store:
                    current_time = datetime.now()
                    to_remove = []

                    for user_id, data in self._memory_store['registration_states'].items():
                        try:
                            updated_at = datetime.fromisoformat(data.get('updated_at', ''))
                            days_diff = (current_time - updated_at).days

                            if (days_diff > days_old and
                                data.get('status') != 'registered'):
                                to_remove.append(user_id)
                        except Exception:
                            continue

                    for user_id in to_remove:
                        del self._memory_store['registration_states'][user_id]
                        cleanup_count += 1

            logger.info(f"Cleaned up {cleanup_count} old registration states")
            return cleanup_count

        except Exception as e:
            logger.error(f"Failed to cleanup old registration states: {e}")
            return 0
