# Quick Start Guide

## Prerequisites

### System Requirements
- Python 3.8+
- Node.js 16+
- Docker & Docker Compose
- PostgreSQL 13+
- Redis 6+

### API Keys Required
- **DeepSeek API Key**: For AI-powered conversation and analysis
- **Twilio Account**: For SMS verification (optional for development)

## Development Setup

### 1. Environment Setup

```bash
# Clone the repository
git clone <repository-url>
cd profiler_agent

# Create Python virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install Python dependencies
cd profilerAgent
pip install -r requirements.txt

# Install Node.js dependencies (if frontend exists)
cd ../frontend
npm install
```

### 2. Environment Configuration

Create `.env` file in the project root:

```bash
# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/dating_app
REDIS_URL=redis://localhost:6379

# AI Service Configuration
DEEPSEEK_API_KEY=your_deepseek_api_key_here
OPENAI_API_KEY=your_openai_key_here  # Optional fallback

# Twilio Configuration (Optional for development)
TWILIO_ACCOUNT_SID=your_twilio_sid
TWILIO_AUTH_TOKEN=your_twilio_token
TWILIO_PHONE_NUMBER=your_twilio_phone

# Application Configuration
SECRET_KEY=your-secret-key-here
DEBUG=true
ENVIRONMENT=development
LOG_LEVEL=INFO
```

### 3. Database Setup

#### Option A: Docker Compose (Recommended)
```bash
# Start PostgreSQL and Redis
docker-compose up -d postgres redis

# Wait for services to be ready
sleep 10

# Run database migrations
cd profilerAgent
python -c "
import asyncio
from agent.core.database_manager import DatabaseManager

async def setup():
    db = DatabaseManager()
    await db.connect()
    print('Database connected successfully!')

asyncio.run(setup())
"
```

#### Option B: Local Installation
```bash
# Install PostgreSQL and Redis locally
# Create database
createdb dating_app

# Start Redis
redis-server
```

### 4. Running Tests

```bash
cd profilerAgent

# Run core component tests
python -m pytest tests/test_core/ -v

# Run specific test
python -m pytest tests/test_core/test_database_manager.py -v

# Run agent logic tests
python -m pytest tests/test_services/test_agent_logic.py -v
```

### 5. Starting the Application

#### Backend (FastAPI)
```bash
cd profilerAgent
python -m uvicorn backend.main:app --reload --host 0.0.0.0 --port 8000
```

#### Frontend (Vue.js) - If Available
```bash
cd frontend
npm run dev
```

## Testing the AI Agents

### 1. Basic Agent Testing

```python
# Test script: test_agents.py
import asyncio
from agent.services.unified_agent import UnifiedAgent
from agent.core.database_manager import DatabaseManager
from agent.core.registration_state import RegistrationStateManager
from agent.core.profile_manager import ProfileManager
from agent.core.memory_manager import MemoryManager
from agent.services.ai_service import AIService

async def test_registration_flow():
    # Initialize components
    db_manager = DatabaseManager()
    await db_manager.connect()
    
    # Create mock Redis client
    class MockRedis:
        def __init__(self):
            self.storage = {}
        async def get(self, key): return self.storage.get(key)
        async def set(self, key, value): self.storage[key] = value; return True
        async def setex(self, key, ttl, value): self.storage[key] = value; return True
        async def delete(self, key): self.storage.pop(key, None); return 1
    
    redis_client = MockRedis()
    
    # Initialize managers
    registration_manager = RegistrationStateManager(redis_client, db_manager)
    profile_manager = ProfileManager(db_manager, redis_client)
    memory_manager = MemoryManager(redis_client, db_manager)
    ai_service = AIService()
    
    # Create unified agent
    agent = UnifiedAgent(
        registration_manager=registration_manager,
        profile_manager=profile_manager,
        memory_manager=memory_manager,
        db_manager=db_manager,
        ai_service=ai_service
    )
    
    # Test conversation flow
    user_id = "test_user_001"
    
    # Start conversation
    response = await agent.start_conversation(user_id, phone_number="+**********")
    print(f"Start: {response.content}")
    
    # Simulate user responses
    test_inputs = [
        "My name is John",
        "I'm 28 years old",
        "I live in New York",
        "I'm a software engineer",
        "I like programming and reading"
    ]
    
    for user_input in test_inputs:
        response = await agent.process_input(user_id, user_input)
        print(f"User: {user_input}")
        print(f"Agent: {response.content}")
        print(f"Progress: {response.progress}")
        print("---")
        
        if response.registration_complete:
            print("Registration completed!")
            break

# Run the test
if __name__ == "__main__":
    asyncio.run(test_registration_flow())
```

### 2. API Testing with curl

```bash
# Health check
curl http://localhost:8000/health

# Start conversation
curl -X POST http://localhost:8000/api/v1/conversations/start \
  -H "Content-Type: application/json" \
  -d '{"user_id": "test_user", "phone_number": "+**********"}'

# Send message
curl -X POST http://localhost:8000/api/v1/conversations/message \
  -H "Content-Type: application/json" \
  -d '{"user_id": "test_user", "message": "My name is John"}'
```

## Common Issues & Solutions

### 1. Database Connection Issues

```bash
# Check if PostgreSQL is running
docker ps | grep postgres

# Check database connectivity
psql -h localhost -U postgres -d dating_app -c "SELECT 1;"

# Reset database
docker-compose down
docker-compose up -d postgres
```

### 2. Redis Connection Issues

```bash
# Check Redis connectivity
redis-cli ping

# Clear Redis cache
redis-cli flushall
```

### 3. AI Service Issues

```bash
# Test DeepSeek API key
curl -X POST "https://api.deepseek.com/v1/chat/completions" \
  -H "Authorization: Bearer $DEEPSEEK_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"model": "deepseek-chat", "messages": [{"role": "user", "content": "Hello"}]}'
```

### 4. Import Errors

```bash
# Ensure Python path is correct
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# Install missing dependencies
pip install -r requirements.txt
```

## Development Workflow

### 1. Adding New Features

```bash
# Create feature branch
git checkout -b feature/new-agent-capability

# Write tests first
# tests/test_services/test_new_feature.py

# Implement feature
# agent/services/new_feature.py

# Run tests
python -m pytest tests/test_services/test_new_feature.py -v

# Update documentation
# docs/feature_documentation.md
```

### 2. Code Quality Checks

```bash
# Format code
black agent/ tests/

# Check imports
isort agent/ tests/

# Lint code
flake8 agent/ tests/

# Type checking (if using mypy)
mypy agent/
```

### 3. Testing Strategy

```bash
# Unit tests
python -m pytest tests/test_core/ -v

# Integration tests
python -m pytest tests/test_services/ -v

# End-to-end tests
python -m pytest tests/test_e2e/ -v

# Coverage report
python -m pytest --cov=agent tests/ --cov-report=html
```

## Production Deployment

### 1. Docker Build

```bash
# Build application image
docker build -t dating-agent:latest .

# Run with docker-compose
docker-compose up -d
```

### 2. Environment Variables for Production

```bash
# Production .env
DATABASE_URL=***********************************/dating_app
REDIS_URL=redis://prod-redis:6379
DEEPSEEK_API_KEY=prod_api_key
SECRET_KEY=production-secret-key
DEBUG=false
ENVIRONMENT=production
LOG_LEVEL=WARNING
```

### 3. Health Checks

```bash
# Application health
curl http://localhost:8000/health

# Database health
curl http://localhost:8000/health/db

# Redis health
curl http://localhost:8000/health/redis
```

## Monitoring & Debugging

### 1. Log Analysis

```bash
# View application logs
docker-compose logs -f app

# View database logs
docker-compose logs -f postgres

# Search for errors
docker-compose logs app | grep ERROR
```

### 2. Performance Monitoring

```bash
# Monitor resource usage
docker stats

# Database performance
docker exec -it postgres_container psql -U postgres -c "
SELECT query, calls, total_time, mean_time 
FROM pg_stat_statements 
ORDER BY total_time DESC LIMIT 10;"
```

### 3. Debugging Tips

```python
# Enable debug logging
import logging
logging.basicConfig(level=logging.DEBUG)

# Add breakpoints
import pdb; pdb.set_trace()

# Profile performance
import cProfile
cProfile.run('your_function()')
```

## Next Steps

1. **Explore the codebase**: Start with `agent/services/unified_agent.py`
2. **Run the tests**: Understand the system through tests
3. **Read the architecture docs**: `docs/AI_Agent_Architecture.md`
4. **Implement new features**: Follow the development workflow
5. **Deploy to production**: Use the deployment guide

## Getting Help

- **Documentation**: Check `docs/` directory
- **Tests**: Look at test files for usage examples
- **Code comments**: Inline documentation in source files
- **Architecture diagrams**: Visual guides in documentation

Happy coding! 🚀
